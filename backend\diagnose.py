#!/usr/bin/env python3
"""
系统诊断工具
用于诊断端口占用、进程状态和套接字问题
"""

import sys
import subprocess
import socket
import time
import os

def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def check_port_status(port=5000):
    """检查端口状态"""
    print_header(f"端口 {port} 状态检查")
    
    # 检查端口是否可用
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            if result == 0:
                print(f"❌ 端口 {port} 被占用")
                return False
            else:
                print(f"✅ 端口 {port} 可用")
                return True
    except Exception as e:
        print(f"⚠️  检查端口时出错: {e}")
        return False

def check_processes():
    """检查相关进程"""
    print_header("相关进程检查")
    
    try:
        # 检查Python进程
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'], 
                              capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:  # 有标题行
                print("🐍 Python进程:")
                for line in lines[1:]:  # 跳过标题行
                    parts = line.split('","')
                    if len(parts) >= 2:
                        name = parts[0].replace('"', '')
                        pid = parts[1].replace('"', '')
                        print(f"   - {name} (PID: {pid})")
            else:
                print("✅ 没有运行的Python进程")
        
        # 检查Flask相关进程
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            flask_connections = []
            for line in lines:
                if ':5000' in line or ':5001' in line or ':5002' in line:
                    flask_connections.append(line.strip())
            
            if flask_connections:
                print("\n🌐 Flask相关网络连接:")
                for conn in flask_connections:
                    print(f"   {conn}")
            else:
                print("\n✅ 没有Flask相关网络连接")
    
    except Exception as e:
        print(f"⚠️  检查进程时出错: {e}")

def check_mongodb():
    """检查MongoDB连接"""
    print_header("MongoDB连接检查")
    
    try:
        import pymongo
        client = pymongo.MongoClient('mongodb://localhost:27017', serverSelectionTimeoutMS=3000)
        client.server_info()
        print("✅ MongoDB连接正常")
        
        # 检查数据库
        db = client['med_call_records']
        collections = db.list_collection_names()
        print(f"📊 数据库集合: {collections}")
        
        client.close()
        
    except ImportError:
        print("⚠️  pymongo未安装")
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")

def kill_port_processes(port=5000):
    """杀死占用端口的进程"""
    print_header(f"清理端口 {port} 占用进程")
    
    try:
        # 查找占用端口的进程
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            pids_to_kill = set()
            
            for line in lines:
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        if pid.isdigit():
                            pids_to_kill.add(pid)
            
            if pids_to_kill:
                print(f"发现占用端口的进程: {list(pids_to_kill)}")
                
                for pid in pids_to_kill:
                    try:
                        subprocess.run(['taskkill', '/F', '/PID', pid], 
                                     capture_output=True, shell=True)
                        print(f"✅ 已终止进程 {pid}")
                    except Exception as e:
                        print(f"⚠️  终止进程 {pid} 失败: {e}")
            else:
                print(f"✅ 没有找到占用端口 {port} 的进程")
    
    except Exception as e:
        print(f"⚠️  清理进程时出错: {e}")

def cleanup_temp_files():
    """清理临时文件"""
    print_header("清理临时文件")
    
    temp_patterns = [
        '*.pyc',
        '__pycache__',
        '*.log',
        '.DS_Store'
    ]
    
    current_dir = os.getcwd()
    print(f"📁 当前目录: {current_dir}")
    
    for root, dirs, files in os.walk(current_dir):
        # 清理__pycache__目录
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            try:
                import shutil
                shutil.rmtree(pycache_path)
                print(f"🗑️  删除: {pycache_path}")
            except Exception as e:
                print(f"⚠️  删除 {pycache_path} 失败: {e}")

def run_diagnostic():
    """运行完整诊断"""
    print("🏥 医疗随访管理系统 - 系统诊断工具")
    print(f"⏰ 诊断时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查端口状态
    port_available = check_port_status(5000)
    
    # 2. 检查进程
    check_processes()
    
    # 3. 检查MongoDB
    check_mongodb()
    
    # 4. 如果端口被占用，提供清理选项
    if not port_available:
        print_header("端口冲突解决方案")
        print("发现端口冲突，建议解决方案：")
        print("1. 使用 'python diagnose.py --kill' 自动清理占用进程")
        print("2. 手动结束相关Python进程")
        print("3. 使用智能启动器自动选择其他端口")
    
    # 5. 清理建议
    print_header("系统优化建议")
    print("✅ 建议使用 smart_start.py 启动应用")
    print("✅ 建议定期清理临时文件")
    print("✅ 建议使用虚拟环境管理依赖")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == '--kill':
        kill_port_processes(5000)
        cleanup_temp_files()
        print("\n🎉 清理完成，可以重新启动应用")
    else:
        run_diagnostic()
        
        print(f"\n{'='*60}")
        print("💡 使用提示:")
        print("   python diagnose.py        # 运行诊断")
        print("   python diagnose.py --kill # 清理并修复")
        print("   python smart_start.py     # 智能启动应用")
        print(f"{'='*60}")

if __name__ == '__main__':
    main() 