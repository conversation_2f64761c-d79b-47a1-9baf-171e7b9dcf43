// 患者信息类型
export interface Patient {
  _id: string;
  name: string;
  phone: string;
  age: number;
  gender: '男' | '女';
  enrollment_date: string;
  training_status: '未开始' | '训练中' | '暂停' | '终止' | '休息';
  // 动态添加的字段
  callStatus?: string;
  callStatusType?: 'active' | 'pending' | 'inactive' | 'completed';
  yesterdayTraining?: string; // 实际存储的是今日最高训练次数
  needsDoctorIntervention?: string;
  todayCallCount?: number;
}

// 通话记录类型
export interface CallRecord {
  _id: string;
  患者名字?: string;
  手机号?: string;
  记录日期?: string;
  通话时间?: string;
  拨号状态?: string;
  训练完成情况?: string;
  训练次数?: string;
  训练时长?: string;
  依从性?: string;
  是否有不适感?: string;
  不适感内容?: string;
  锻炼后是否有缓解?: string;
  锻炼辅助仪器是否有问题?: string;
  锻炼辅助仪器问题内容?: string;
  设备使用情况?: string;
  是否需要医生人工和患者联系?: string;
  医生建议?: string;
  康复效果评估?: string;
  疼痛改善情况?: string;
  活动能力变化?: string;
  对话历史记录?: Array<{
    role: 'assistant' | 'user';
    content: string;
  }>;
}

// 统计数据类型
export interface StatsData {
  totalPersonnel: number;
  pendingToday: number;
  completedToday: number;
  avgTraining: number;
  completionRate: number;
  pendingTrend?: TrendData;
  completedTrend?: TrendData;
  completionRateTrend?: TrendData;
  avgTrainingTrend?: TrendData;
}

// 趋势数据类型
export interface TrendData {
  change: number;
  isUp: boolean;
  text: string;
}

// 康复进度类型
export interface ProgressData {
  currentDay: number;
  totalDays: number;
  compliance: number;
  todayTraining: number;
  avgTraining: number;
  totalCallDays?: number;
  totalTrainingDays?: number;
  records?: Array<{
    date: string;
    training: number;
    completed: boolean;
  }>;
}

// 训练统计数据类型
export interface TrainingStats {
  completedCount: number;
  totalCount: number;
  avgTrainingCount: number;
  minTrainingCount: number;
  maxTrainingCount: number;
  discomfortCount: number;
  deviceIssueCount: number;
  doctorInterventionCount: number;
}

// 筛选条件类型
export interface FilterOptions {
  startDate: string;
  endDate: string;
  callType: string;
  status: string;
}

// 随访统计数据接口
export interface FollowUpStat {
  time: string;
  time_key: string;
  total_follow_up_people: number;
  total_calls: number;
  connected_people: number;
  training_people_count: number;
  doctor_intervention_people: number;
} 