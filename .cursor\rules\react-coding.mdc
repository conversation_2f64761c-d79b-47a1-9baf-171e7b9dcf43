---
description: 
globs: 
alwaysApply: true
---
# React + TypeScript 编码规范

## 组件开发规范

### 组件文件结构
- 使用函数组件和React Hooks
- 组件文件使用 `.tsx` 扩展名
- 每个组件文件应该只导出一个主要组件
- 组件名使用PascalCase命名

### TypeScript类型定义
- 所有props必须有TypeScript类型定义
- 复杂类型定义放在 [src/types/](mdc:src/types/) 目录
- 使用interface定义组件props
- 善用泛型提高代码复用性

### 状态管理
- 使用React Query进行服务端状态管理
- 本地状态使用useState和useReducer
- 复杂状态逻辑抽取到自定义Hooks
- 自定义Hooks放在 [src/hooks/](mdc:src/hooks/) 目录

### 样式规范
- 使用Tailwind CSS进行样式编写
- 复用样式通过组件封装，避免重复类名
- 使用 [tailwind.config.js](mdc:tailwind.config.js) 自定义主题
- 响应式设计使用Tailwind的响应式前缀

### 组件库使用
- 优先使用Radix UI组件库
- 参考 [components.json](mdc:components.json) 了解shadcn/ui配置
- 自定义组件放在 [src/components/](mdc:src/components/) 目录

## 表单处理规范
- 使用React Hook Form处理表单
- 使用Zod进行表单验证
- 表单组件应该有明确的错误处理

## 数据获取规范
- 使用Axios进行HTTP请求
- API调用使用React Query包装
- 错误处理要统一和完善
- 加载状态要有适当的UI反馈

## 文件组织规范
- 页面组件放在 [src/pages/](mdc:src/pages/) 目录
- 可复用组件放在 [src/components/](mdc:src/components/) 目录
- 工具函数放在 [src/lib/](mdc:src/lib/) 目录
- 静态资源放在 [src/assets/](mdc:src/assets/) 目录

## 代码质量
- 遵循ESLint规则配置 [eslint.config.js](mdc:eslint.config.js)
- 使用TypeScript严格模式
- 组件要有适当的错误边界处理

- 重要逻辑要有注释说明