from flask import Flask
from flask_cors import CORS
import logging
import sys
import os

from config import Config
from database import init_db

# 添加AICall工程目录到Python路径
AI_CALL_PATH = r'/home/<USER>/workspace/suifangmedcall'
if AI_CALL_PATH not in sys.path:
    sys.path.append(AI_CALL_PATH)

# 导入路由模块
from routes.auth_routes import auth_bp
from routes.user_routes import user_bp
from routes.personnel_routes import personnel_bp
from routes.stats_routes import stats_bp
from routes.scheduler_routes import scheduler_bp

def create_app():
    """应用工厂函数"""
    app = Flask(__name__)
    
    # 配置应用
    app.config['JWT_SECRET_KEY'] = Config.JWT_SECRET_KEY
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = Config.JWT_ACCESS_TOKEN_EXPIRES
    
    # 配置日志
    logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL.upper()))
    app.logger.setLevel(getattr(logging, Config.LOG_LEVEL.upper()))
    
    # 启用CORS - 允许所有源访问（生产环境应该限制具体域名）
    CORS(app, resources={
        r"/api/*": {
            "origins": "*",
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })
    
    # 注册蓝图（路由模块）
    app.register_blueprint(auth_bp)
    app.register_blueprint(user_bp)
    app.register_blueprint(personnel_bp)
    app.register_blueprint(stats_bp)
    app.register_blueprint(scheduler_bp)
    
    return app

if __name__ == '__main__':
    # 创建应用实例
    app = create_app()
    
    # 初始化数据库
    init_db()
    
    # 启动应用
    app.run(
        debug=Config.DEBUG,
        host=Config.HOST,
        port=Config.PORT
    ) 