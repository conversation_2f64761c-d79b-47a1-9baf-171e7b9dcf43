import pymongo
from datetime import datetime
from config import Config
from auth.auth import hash_password
import logging
import atexit

logger = logging.getLogger(__name__)

# MongoDB连接配置
MONGO_CONFIG = {
    'serverSelectionTimeoutMS': 5000,
    'connectTimeoutMS': 5000,
    'socketTimeoutMS': 10000,
    'maxPoolSize': 50,
    'minPoolSize': 5,
    'maxIdleTimeMS': 30000,
    'waitQueueTimeoutMS': 5000
}

# MongoDB连接
try:
    client = pymongo.MongoClient(Config.MONGO_URL, **MONGO_CONFIG)
    # 测试连接
    client.server_info()
    db = client[Config.DATABASE_NAME]
    logger.info(f"✅ MongoDB连接成功: {Config.MONGO_URL}")
except Exception as e:
    logger.error(f"❌ MongoDB连接失败: {e}")
    client = None
    db = None

def cleanup_db_connection():
    """清理数据库连接"""
    global client
    if client:
        try:
            client.close()
            logger.info("✅ 数据库连接已关闭")
        except Exception as e:
            logger.warning(f"⚠️  关闭数据库连接时出错: {e}")

# 注册程序退出时的清理函数
atexit.register(cleanup_db_connection)

def init_db():
    """初始化数据库，插入mock数据"""
    if not client or not db:
        logger.error("❌ 数据库连接不可用，无法初始化")
        return False
    
    try:
        # Mock数据用于初始化
        from mock_data import get_mock_personnel, get_mock_call_records
        
        # 检查是否已有数据
        personnel_count = db.personnel.count_documents({})
        if personnel_count == 0:
            # 插入mock患者数据
            mock_personnel = get_mock_personnel()
            db.personnel.insert_many(mock_personnel)
            logger.info("已插入mock患者数据")
            
            # 插入mock通话记录数据
            mock_records = get_mock_call_records()
            db.call_records.insert_many(mock_records)
            logger.info("已插入mock通话记录数据")
        
        # 检查是否有用户账号，如果没有则创建默认账号
        user_count = db.sys_info.count_documents({'doc_type': 'user_account'})
        if user_count == 0:
            # 创建默认管理员账号
            admin_user = {
                'doc_type': 'user_account',
                'username': 'admin',
                'password_hash': hash_password('admin123'),
                'role': '管理员',
                'full_name': '系统管理员',
                'email': '<EMAIL>',
                'is_active': True,
                'login_count': 0,
                'last_login': None,
                'created_at': datetime.utcnow()
            }
            
            # 创建默认医生账号
            doctor_user = {
                'doc_type': 'user_account',
                'username': 'doctor',
                'password_hash': hash_password('doctor123'),
                'role': '医生',
                'full_name': '测试医生',
                'email': '<EMAIL>',
                'is_active': True,
                'login_count': 0,
                'last_login': None,
                'created_at': datetime.utcnow()
            }
            
            db.sys_info.insert_many([admin_user, doctor_user])
            logger.info("已创建默认用户账号")
            logger.info("管理员账号: admin / admin123")
            logger.info("医生账号: doctor / doctor123")
            
        logger.info("✅ 数据库初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化错误: {e}")
        return False

def get_db():
    """获取数据库连接"""
    if not db:
        logger.error("❌ 数据库连接不可用")
        return None
    return db 
 