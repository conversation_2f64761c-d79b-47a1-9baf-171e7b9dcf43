#!/usr/bin/env python3
"""
智能启动脚本 - 医疗随访管理系统后端
集成端口冲突处理、数据库连接管理和异常处理
"""

import os
import sys
import logging
import signal
import time
import threading
from pathlib import Path
from typing import Optional

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('app.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class SmartFlaskStarter:
    """智能Flask启动器"""
    
    def __init__(self):
        self.app = None
        self.final_port = None
        self.shutdown_event = threading.Event()
        
    def setup_environment(self):
        """设置环境和路径"""
        logger.info("🔧 设置环境配置...")
        
        # 添加AICall工程目录到Python路径
        ai_call_path = r'/home/<USER>/workspace/suifangmedcall'
        
        if not os.path.exists(ai_call_path):
            logger.warning(f"⚠️  AICall工程目录不存在: {ai_call_path}")
            logger.warning("调度器功能将不可用，但Flask应用仍可正常运行")
        else:
            logger.info(f"✅ AICall工程目录存在")
            if ai_call_path not in sys.path:
                sys.path.append(ai_call_path)
                logger.info(f"✅ 已添加到Python路径")
    
    def check_dependencies(self) -> bool:
        """检查依赖项"""
        logger.info("📦 检查依赖项...")
        
        required_packages = [
            'flask', 'flask_cors', 'pymongo', 
            'motor', 'jwt', 'bcrypt', 'requests', 'psutil'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                logger.debug(f"✅ {package}")
            except ImportError:
                logger.error(f"❌ {package}")
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"⚠️  缺少依赖项: {', '.join(missing_packages)}")
            logger.error("请运行: pip install -r requirements.txt")
            return False
        
        logger.info("✅ 所有依赖项已安装")
        return True
    
    def handle_port_conflict(self) -> int:
        """处理端口冲突"""
        logger.info("🔍 检查端口可用性...")
        
        try:
            from utils.port_manager import PortManager
            
            default_port = 5000
            port, message = PortManager.handle_port_conflict(default_port, auto_resolve=True)
            
            if port != default_port:
                logger.warning(f"⚠️  {message}")
            else:
                logger.info(f"✅ {message}")
            
            return port
            
        except ImportError:
            logger.warning("⚠️  端口管理器不可用，使用默认端口")
            return 5000
        except Exception as e:
            logger.error(f"❌ 端口检查失败: {e}")
            return 5000
    
    def test_database_connection(self) -> bool:
        """测试数据库连接"""
        logger.info("🔗 测试数据库连接...")
        
        try:
            import pymongo
            from config import Config
            
            # 测试MongoDB连接
            mongo_url = Config.MONGO_URL
            client = pymongo.MongoClient(mongo_url, serverSelectionTimeoutMS=5000)
            client.server_info()  # 触发连接
            
            logger.info(f"✅ MongoDB连接成功: {mongo_url}")
            
            # 测试数据库操作
            db = client[Config.DATABASE_NAME]
            db.test_collection.find_one()
            
            logger.info(f"✅ 数据库 {Config.DATABASE_NAME} 可访问")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            logger.error("请确保MongoDB服务正在运行")
            return False
    
    def graceful_shutdown(self, signum, frame):
        """优雅关闭处理"""
        logger.info(f"🛑 接收到信号 {signum}，开始优雅关闭...")
        
        self.shutdown_event.set()
        
        # 清理资源
        try:
            # 关闭数据库连接
            from database import client
            if client:
                client.close()
                logger.info("✅ 数据库连接已关闭")
        except Exception as e:
            logger.warning(f"⚠️  关闭数据库连接时出错: {e}")
        
        # 清理端口
        try:
            from utils.port_manager import PortManager
            if self.final_port:
                PortManager.cleanup_socket_connections()
                logger.info(f"✅ 清理端口 {self.final_port} 连接")
        except Exception as e:
            logger.warning(f"⚠️  清理端口连接时出错: {e}")
        
        logger.info("👋 应用已安全关闭")
        sys.exit(0)
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        signal.signal(signal.SIGINT, self.graceful_shutdown)
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, self.graceful_shutdown)
    
    def create_flask_app(self):
        """创建Flask应用"""
        logger.info("🏗️  创建Flask应用...")
        
        try:
            from app import create_app
            from database import init_db
            
            # 创建应用
            self.app = create_app()
            
            # 初始化数据库
            logger.info("🔗 初始化数据库...")
            init_db()
            logger.info("✅ 数据库初始化完成")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建Flask应用失败: {e}")
            return False
    
    def start_server(self, port: int):
        """启动服务器"""
        logger.info("🚀 启动Flask服务器...")
        logger.info(f"📍 访问地址: http://localhost:{port}")
        logger.info(f"📋 API文档: http://localhost:{port}/api")
        logger.info("🛑 按 Ctrl+C 停止服务器")
        
        try:
            self.app.run(
                debug=True,  # 生产环境关闭debug
                host='0.0.0.0',
                port=port,
                use_reloader=False,  # 关闭自动重载避免端口冲突
                threaded=True
            )
        except OSError as e:
            if "WinError 10038" in str(e):
                logger.error("❌ 套接字错误：可能是端口冲突或连接未正确关闭")
                logger.info("🔧 尝试清理端口连接...")
                
                try:
                    from utils.port_manager import PortManager
                    PortManager.cleanup_socket_connections()
                    time.sleep(2)
                    
                    # 重试启动
                    logger.info("🔄 重新尝试启动服务器...")
                    self.app.run(
                        debug=False,
                        host='0.0.0.0',
                        port=port,
                        use_reloader=False,
                        threaded=True
                    )
                except Exception as retry_e:
                    logger.error(f"❌ 重试启动失败: {retry_e}")
                    raise
            else:
                raise
        except Exception as e:
            logger.error(f"❌ 启动服务器失败: {e}")
            raise
    
    def run(self):
        """主运行方法"""
        logger.info("🏥 医疗随访管理系统 - 智能启动器")
        logger.info("=" * 60)
        
        try:
            # 设置信号处理器
            self.setup_signal_handlers()
            
            # 1. 环境设置
            self.setup_environment()
            
            # 2. 检查依赖
            if not self.check_dependencies():
                sys.exit(1)
            
            # 3. 测试数据库连接
            if not self.test_database_connection():
                logger.warning("⚠️  数据库连接失败，但仍会尝试启动应用")
            
            # 4. 处理端口冲突
            self.final_port = self.handle_port_conflict()
            
            # 5. 创建Flask应用
            if not self.create_flask_app():
                sys.exit(1)
            
            logger.info("🎉 所有检查通过，准备启动服务器...")
            logger.info("=" * 60)
            
            # 6. 启动服务器
            self.start_server(self.final_port)
            
        except KeyboardInterrupt:
            logger.info("🛑 用户中断，开始关闭...")
            self.graceful_shutdown(signal.SIGINT, None)
        except Exception as e:
            logger.error(f"❌ 启动失败: {e}")
            logger.exception("详细错误信息:")
            sys.exit(1)

def main():
    """主函数"""
    starter = SmartFlaskStarter()
    starter.run()

if __name__ == '__main__':
    main() 