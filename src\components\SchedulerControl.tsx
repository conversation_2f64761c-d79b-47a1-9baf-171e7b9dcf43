import React, { useState } from 'react';
import { Play, Square, RotateCcw, Phone, Users, Activity, AlertCircle, CheckCircle } from 'lucide-react';
import { 
  schedulerService,
  handleSchedulerError,
  formatSuccessMessage,
  formatErrorMessage
} from '../services/schedulerService';
import { useSchedulerStatus, forceRefreshGlobalStatus } from '../hooks/useSchedulerStatus';

const SchedulerControl: React.FC = () => {
  const { 
    status, 
    loading, 
    error, 
    lastUpdateTime, 
    initialCheckComplete, 
    fetchStatus,
    isRunning,
    isStopped
  } = useSchedulerStatus({
    autoRefresh: true,
    refreshInterval: 10000 // 10秒刷新
  });

  const [operationLoading, setOperationLoading] = useState({
    start: false,
    stop: false,
    restart: false,
    refresh: false,
    call: false
  });
  const [immediateCallPhone, setImmediateCallPhone] = useState('');

  // 执行调度器操作的通用函数
  const executeOperation = async (
    operation: string,
    operationFunc: () => Promise<any>,
    confirmMessage?: string
  ) => {
    if (confirmMessage && !confirm(confirmMessage)) return;
    
    setOperationLoading(prev => ({ ...prev, [operation]: true }));
    try {
      const data = await operationFunc();
      
      if (data.success) {
        if (operation !== 'refresh') {
          // 操作成功后立即刷新状态
          await forceRefreshGlobalStatus();
        }
        alert(formatSuccessMessage(operation));
      } else {
        alert(formatErrorMessage(operation, data.message));
      }
    } catch (err) {
      alert(formatErrorMessage(operation, handleSchedulerError(err)));
    } finally {
      setOperationLoading(prev => ({ ...prev, [operation]: false }));
    }
  };

  // 启动调度器
  const handleStart = () => executeOperation(
    'start',
    () => schedulerService.start()
  );

  // 停止调度器
  const handleStop = () => executeOperation(
    'stop',
    () => schedulerService.stop(),
    '确定要停止AI电话机器人吗？'
  );

  // 重启调度器
  const handleRestart = () => executeOperation(
    'restart',
    () => schedulerService.restart(),
    '确定要重启AI电话机器人吗？'
  );

  // 刷新人员名单
  const handleRefresh = () => executeOperation(
    'refresh',
    () => schedulerService.refreshPersonnel()
  );

  // 立即拨号
  const handleImmediateCall = async () => {
    const phone = immediateCallPhone.trim();
    
    if (!phone) {
      alert('请输入电话号码');
      return;
    }

    if (!schedulerService.validatePhoneNumber(phone)) {
      alert('请输入正确的手机号码格式（如：13800138000）');
      return;
    }

    if (!confirm(`确定要立即拨打电话 ${phone} 吗？`)) return;
    
    setOperationLoading(prev => ({ ...prev, call: true }));
    try {
      const data = await schedulerService.makeImmediateCall(phone);
      
      if (data.success) {
        alert(formatSuccessMessage('call'));
        setImmediateCallPhone('');
      } else {
        alert(formatErrorMessage('call', data.message));
      }
    } catch (err) {
      alert(formatErrorMessage('call', handleSchedulerError(err)));
    } finally {
      setOperationLoading(prev => ({ ...prev, call: false }));
    }
  };

  // 判断启动按钮是否应该禁用
  const isStartDisabled = () => {
    return operationLoading.start || 
           isRunning || 
           !initialCheckComplete ||
           loading;
  };

  // 判断停止按钮是否应该禁用
  const isStopDisabled = () => {
    return operationLoading.stop || 
           isStopped || 
           !status ||
           !initialCheckComplete ||
           loading;
  };

  // 初始加载状态
  if (loading && !initialCheckComplete) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-3 text-gray-600">正在检查机器人状态...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-slate-800">AI电话机器人管理</h2>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Activity className="h-4 w-4" />
          <span>自动刷新：每10秒</span>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <div className="flex-1">
              <span className="text-red-700 font-medium">连接失败</span>
              <p className="text-red-600 text-sm mt-1">{error}</p>
              <p className="text-red-500 text-xs mt-1">
                请检查后端服务是否运行，或联系系统管理员
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 状态显示区域 */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-800">运行状态</h3>
          <button
            onClick={() => fetchStatus()}
            className="text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400"
            disabled={loading}
          >
            {loading ? '刷新中...' : '手动刷新'}
          </button>
        </div>

        {status ? (
          <div className="space-y-3">
            <div className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              <span className="font-medium">状态：</span>
              <span className={`ml-2 px-3 py-1 rounded-full text-sm font-medium ${
                schedulerService.getStatusColorClass(status)
              }`}>
                {schedulerService.formatStatus(status)}
              </span>
              {isRunning && (
                <div className="ml-2 w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              )}
            </div>

            {status.pid && (
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-gray-500" />
                <span className="font-medium">进程ID：</span>
                <span className="ml-2 font-mono text-sm bg-gray-200 px-2 py-1 rounded">
                  {status.pid}
                </span>
              </div>
            )}

            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-gray-500" />
              <span className="font-medium">消息：</span>
              <span className="ml-2 text-gray-700">{status.message}</span>
            </div>

            {lastUpdateTime && (
              <div className="flex items-center text-sm text-gray-500">
                <span>最后更新：{lastUpdateTime.toLocaleString()}</span>
              </div>
            )}
          </div>
        ) : (
          <div className="text-gray-500 text-center py-4">
            {error ? '无法获取状态信息' : '正在获取状态...'}
          </div>
        )}
      </div>

      {/* 控制按钮区域 */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-800 mb-4">控制操作</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <button
            onClick={handleStart}
            disabled={isStartDisabled()}
            className="flex items-center justify-center px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            title={isRunning ? '调度器已在运行' : '启动AI电话机器人'}
          >
            {operationLoading.start ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            启动
          </button>

          <button
            onClick={handleStop}
            disabled={isStopDisabled()}
            className="flex items-center justify-center px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            title={isStopped ? '调度器已停止' : '停止AI电话机器人'}
          >
            {operationLoading.stop ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Square className="h-4 w-4 mr-2" />
            )}
            停止
          </button>

          <button
            onClick={handleRestart}
            disabled={operationLoading.restart || !initialCheckComplete || loading}
            className="flex items-center justify-center px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            title="重启AI电话机器人"
          >
            {operationLoading.restart ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <RotateCcw className="h-4 w-4 mr-2" />
            )}
            重启
          </button>

          <button
            onClick={handleRefresh}
            disabled={operationLoading.refresh || !initialCheckComplete || loading}
            className="flex items-center justify-center px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            title="刷新人员名单数据"
          >
            {operationLoading.refresh ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Users className="h-4 w-4 mr-2" />
            )}
            刷新名单
          </button>
        </div>
        
        {!initialCheckComplete && (
          <p className="text-sm text-gray-500 mt-2">
            ⏳ 正在检查系统状态，请稍候...
          </p>
        )}
      </div>

      {/* 立即拨号功能 */}
      <div>
        <h3 className="text-lg font-medium text-gray-800 mb-4">立即拨号</h3>
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <input
              type="tel"
              value={immediateCallPhone}
              onChange={(e) => setImmediateCallPhone(e.target.value)}
              placeholder="输入手机号码，如：13800138000"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              maxLength={11}
              disabled={!initialCheckComplete || loading}
            />
            <button
              onClick={handleImmediateCall}
              disabled={operationLoading.call || !immediateCallPhone.trim() || !initialCheckComplete || loading}
              className="flex items-center px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              {operationLoading.call ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Phone className="h-4 w-4 mr-2" />
              )}
              立即拨号
            </button>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium mb-1">注意事项：</p>
                <ul className="space-y-1 text-xs">
                  <li>• 请谨慎使用立即拨号功能</li>
                  <li>• 建议先使用测试号码进行验证</li>
                  <li>• 确保电话号码格式正确（11位手机号）</li>
                  <li>• 拨号请求发送后，实际拨号时间可能有延迟</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SchedulerControl; 