---
description: 
globs: 
alwaysApply: true
---
# 医疗随访管理系统项目概览

## 项目简介
这是一个基于React + TypeScript + Vite的医疗随访管理系统前端项目。系统主要用于管理患者信息和通话记录，支持AI智能随访功能。

## 技术栈
- **前端框架**: React 18.3.1 + TypeScript
- **构建工具**: Vite
- **样式方案**: Tailwind CSS + Radix UI组件库
- **状态管理**: React Query用于数据获取和缓存
- **路由**: React Router DOM
- **表单处理**: React Hook Form + Zod验证
- **图表库**: Recharts + Chart.js
- **HTTP请求**: Axios
- **后端框架**:使用Python+Flask+Mongodb

## 项目结构说明
项目入口点是 [index.html](mdc:index.html)，主要的React应用启动文件是 [src/main.tsx](mdc:src/main.tsx)。

核心配置文件：
- [package.json](mdc:package.json) - 项目依赖和脚本配置
- [vite.config.ts](mdc:vite.config.ts) - Vite构建配置
- [tsconfig.json](mdc:tsconfig.json) - TypeScript配置
- [tailwind.config.js](mdc:tailwind.config.js) - Tailwind CSS配置

主要源码目录：
- [src/](mdc:src) - 源代码目录
- [src/components/](mdc:src/components) - React组件
- [src/pages/](mdc:src/pages) - 页面组件
- [src/hooks/](mdc:src/hooks) - 自定义React Hooks
- [src/lib/](mdc:src/lib) - 工具函数和配置
- [src/types/](mdc:src/types) - TypeScript类型定义

## 数据库设计
项目使用MongoDB数据库，主要集合包括：
- **personnel** - 患者基本信息
- **call_records** - 通话记录和训练情况

详细的数据库结构可以参考 [DATABASE_SCHEMA.md](mdc:DATABASE_SCHEMA.md)。

## 开发指南
- 使用 `npm run dev` 启动开发服务器
- 使用 `npm run build` 构建生产版本



- 遵循TypeScript严格模式和ESLint规范-