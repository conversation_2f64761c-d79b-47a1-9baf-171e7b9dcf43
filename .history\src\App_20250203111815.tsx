import { useState } from 'react'
import { Layout } from './components/Layout'
import { URLInput } from './components/URLInput'
import { CrawlList } from './components/CrawlList'
import { Sidebar } from './components/Sidebar'
import { ArticleList } from './components/ArticleList'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"

interface Article {
  title: string
  url: string
  content: string
}

interface CrawlItem {
  url: string
  status: 'pending' | 'crawling' | 'completed' | 'failed'
  articles: Article[]
  stats: {
    totalArticles: number
    totalPages: number
    crawledCount: number
    averageTime?: number
    startTime?: number
  }
}

function App() {
  const [crawlList, setCrawlList] = useState<CrawlItem[]>([])
  const [selectedUrl, setSelectedUrl] = useState<string | null>(null)
  const [selectedArticle, setSelectedArticle] = useState<Article | null>(null)

  const handleAddUrl = async (url: string) => {
    try {
      const response = await fetch('http://localhost:8000/analyze-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      })
      
      const data = await response.json()
      
      if (data.has_articles) {
        setCrawlList(prev => [...prev, {
          url,
          status: 'pending',
          articles: [],
          stats: {
            totalArticles: data.articles.length,
            totalPages: data.totalPages || 1,
            crawledCount: 0
          }
        }])
      } else {
        alert('该网页不包含文章列表：' + data.message)
      }
    } catch (error) {
      alert('添加URL失败：' + error)
    }
  }

  const handleStartCrawl = async () => {
    for (const item of crawlList) {
      if (item.status === 'pending') {
        try {
          // 更新状态为爬取中并记录开始时间
          setCrawlList(prev => prev.map(i => 
            i.url === item.url ? { 
              ...i, 
              status: 'crawling',
              stats: { ...i.stats, startTime: Date.now() }
            } : i
          ))

          const response = await fetch('http://localhost:8000/crawl-articles', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ url: item.url }),
          })

          const articles = await response.json()
          const endTime = Date.now()
          const averageTime = (endTime - (item.stats.startTime || endTime)) / articles.length

          // 更新状态为完成
          setCrawlList(prev => prev.map(i => 
            i.url === item.url ? { 
              ...i, 
              status: 'completed', 
              articles,
              stats: {
                ...i.stats,
                crawledCount: articles.length,
                averageTime
              }
            } : i
          ))
        } catch (error) {
          // 更新状态为失败
          setCrawlList(prev => prev.map(i => 
            i.url === item.url ? { ...i, status: 'failed' } : i
          ))
        }
      }
    }
  }

  const handleDeleteArticle = (url: string, articleUrl: string) => {
    setCrawlList(prev => prev.map(item => 
      item.url === url 
        ? { 
            ...item, 
            articles: item.articles.filter(a => a.url !== articleUrl)
          }
        : item
    ))
  }

  const renderContent = () => {
    if (!selectedUrl) {
      return (
        <>
          <div className="mb-4">
            <URLInput onAdd={handleAddUrl} />
          </div>
          <div className="mb-4">
            <CrawlList 
              items={crawlList}
              onStart={handleStartCrawl}
            />
          </div>
        </>
      )
    }

    const selectedItem = crawlList.find(item => item.url === selectedUrl)
    if (!selectedItem) return null

    return (
      <ArticleList
        articles={selectedItem.articles}
        onDelete={(articleUrl) => handleDeleteArticle(selectedUrl, articleUrl)}
        onViewDetail={setSelectedArticle}
      />
    )
  }

  return (
    <Layout>
      <div className="flex h-screen">
        <Sidebar 
          urls={crawlList.map(item => ({
            url: item.url,
            status: item.status
          }))}
          onSelect={setSelectedUrl}
          selectedUrl={selectedUrl}
        />
        <div className="flex-1 p-4">
          {renderContent()}
        </div>
      </div>

      <Dialog open={!!selectedArticle} onOpenChange={() => setSelectedArticle(null)}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedArticle?.title}</DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            <a
              href={selectedArticle?.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-300 hover:underline block mb-4"
            >
              {selectedArticle?.url}
            </a>
            <div className="prose prose-invert">
              <p className="whitespace-pre-wrap">{selectedArticle?.content}</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Layout>
  )
}

export default App
