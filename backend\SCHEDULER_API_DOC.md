# AI电话机器人调度器管理API文档

## 概述

本文档介绍了集成在Flask应用中的AI电话机器人调度器管理功能。通过这些API接口，可以在系统设置界面对后台运行的电话语音机器人进行管理。

## 前置条件

1. **AICall工程目录**：确保`D:\57_project_codes\phoneAI\AICall\SuiFangMedCall`目录存在
2. **调度器模块**：确保该目录下有`scheduler_manager.py`文件
3. **Python路径**：应用会自动将AICall工程目录添加到Python路径中

## API接口列表

### 1. 健康检查

**接口地址**：`GET /api/scheduler/health`

**功能**：检查调度器API是否正常，以及调度器模块是否可用

**响应示例**：
```json
{
    "success": true,
    "message": "调度器API正常",
    "scheduler_available": true
}
```

### 2. 获取调度器状态

**接口地址**：`GET /api/scheduler/status`

**功能**：获取当前调度器的运行状态

**响应示例**：
```json
{
    "success": true,
    "status": "running",
    "message": "调度器正在运行，进程ID: 12345",
    "pid": 12345
}
```

状态说明：
- `running`：调度器正在运行
- `stopped`：调度器已停止

### 3. 启动调度器

**接口地址**：`POST /api/scheduler/start`

**功能**：启动AI电话机器人调度器

**响应示例**：
```json
{
    "success": true,
    "message": "调度器启动成功，进程ID: 12345",
    "pid": 12345
}
```

### 4. 停止调度器

**接口地址**：`POST /api/scheduler/stop`

**功能**：停止正在运行的调度器

**响应示例**：
```json
{
    "success": true,
    "message": "调度器已停止"
}
```

### 5. 重启调度器

**接口地址**：`POST /api/scheduler/restart`

**功能**：重启调度器（先停止再启动）

**响应示例**：
```json
{
    "success": true,
    "message": "调度器重启成功，新进程ID: 12345"
}
```

### 6. 刷新人员名单

**接口地址**：`POST /api/scheduler/refresh`

**功能**：刷新调度器中的人员名单

**响应示例**：
```json
{
    "success": true,
    "message": "已发送刷新命令到调度器，将在下次拨号时生效"
}
```

### 7. 立即拨号

**接口地址**：`POST /api/scheduler/call/immediate`

**功能**：立即拨打指定的电话号码

**请求参数**：
```json
{
    "phone": "13800138000"
}
```

**响应示例**：
```json
{
    "success": true,
    "message": "成功拨打电话: 13800138000"
}
```

## 错误处理

### 常见错误码

- `400`：请求参数错误
- `500`：服务器内部错误
- `503`：调度器模块不可用

### 调度器模块不可用的情况

如果AICall工程目录不存在或`scheduler_manager`模块无法导入，所有API都会返回503错误：

```json
{
    "success": false,
    "message": "调度器模块不可用，请检查AICall工程目录是否正确配置"
}
```

## 使用示例

### Python示例

```python
import requests

# 基础URL
base_url = "http://localhost:5000/api/scheduler"

# 1. 检查健康状态
response = requests.get(f"{base_url}/health")
print(response.json())

# 2. 获取调度器状态
response = requests.get(f"{base_url}/status")
print(response.json())

# 3. 启动调度器
response = requests.post(f"{base_url}/start")
print(response.json())

# 4. 立即拨号
data = {"phone": "13800138000"}
response = requests.post(f"{base_url}/call/immediate", json=data)
print(response.json())

# 5. 停止调度器
response = requests.post(f"{base_url}/stop")
print(response.json())
```

### JavaScript/前端示例

```javascript
// 获取调度器状态
async function getSchedulerStatus() {
    try {
        const response = await fetch('/api/scheduler/status');
        const data = await response.json();
        console.log('调度器状态:', data);
        return data;
    } catch (error) {
        console.error('获取状态失败:', error);
    }
}

// 启动调度器
async function startScheduler() {
    try {
        const response = await fetch('/api/scheduler/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        const data = await response.json();
        console.log('启动结果:', data);
        return data;
    } catch (error) {
        console.error('启动失败:', error);
    }
}

// 立即拨号
async function makeImmediateCall(phoneNumber) {
    try {
        const response = await fetch('/api/scheduler/call/immediate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ phone: phoneNumber })
        });
        const data = await response.json();
        console.log('拨号结果:', data);
        return data;
    } catch (error) {
        console.error('拨号失败:', error);
    }
}
```

## 测试脚本

项目提供了完整的测试脚本`test_scheduler_api.py`，可以用来验证所有API功能：

```bash
cd backend
python test_scheduler_api.py
```

## 注意事项

1. **权限管理**：建议在前端实现适当的权限控制，只允许管理员使用这些功能
2. **错误处理**：前端应该对API返回的错误进行友好的提示
3. **状态同步**：建议定期调用状态查询接口来同步调度器状态
4. **测试环境**：在使用立即拨号功能时，请使用测试号码避免误拨
5. **日志监控**：可以通过Flask日志查看调度器的运行情况

## 集成到系统设置界面

建议在系统设置界面添加以下功能模块：

1. **调度器状态显示**：实时显示调度器运行状态和进程ID
2. **控制按钮**：提供启动、停止、重启按钮
3. **人员名单刷新**：提供刷新按钮来更新人员名单
4. **立即拨号功能**：提供输入框让管理员可以立即拨打指定号码
5. **状态自动刷新**：定期自动获取调度器状态并更新界面 