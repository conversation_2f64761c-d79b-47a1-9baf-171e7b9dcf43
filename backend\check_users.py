import pymongo
import json

# 连接数据库
client = pymongo.MongoClient('mongodb://localhost:27017')
db = client['med_call_records']

# 查找用户
users = list(db.sys_info.find({'doc_type': 'user_account'}))

print(f'找到的用户数量: {len(users)}')
for user in users:
    print(f'用户: {user.get("username", "N/A")} - 角色: {user.get("role", "N/A")} - 激活: {user.get("is_active", "N/A")}')
    print(f'完整用户信息: {json.dumps({k: v for k, v in user.items() if k != "password_hash"}, indent=2, default=str)}')
    print('---')
    
if len(users) == 0:
    print('没有找到用户，需要运行初始化脚本')
    
# 检查是否有我们预期的admin和doctor用户
admin_user = db.sys_info.find_one({'doc_type': 'user_account', 'username': 'admin'})
doctor_user = db.sys_info.find_one({'doc_type': 'user_account', 'username': 'doctor'})

print(f'\n检查预期用户:')
print(f'admin用户存在: {admin_user is not None}')
print(f'doctor用户存在: {doctor_user is not None}') 