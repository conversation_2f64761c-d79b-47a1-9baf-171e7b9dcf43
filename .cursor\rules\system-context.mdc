---
description: 
globs: 
alwaysApply: true
---
# 医疗随访管理系统业务上下文

## 系统功能模块

### 患者管理
- 患者基本信息的增删改查
- 患者档案管理和维护
- 患者分组和标签管理
- 数据存储在MongoDB的 `personnel` 集合中

### 通话记录管理
- AI智能通话记录的管理
- 通话质量评估和分析
- 训练数据的收集和整理
- 数据存储在MongoDB的 `call_records` 集合中

### 数据可视化
- 使用Recharts和Chart.js展示统计数据
- 患者随访情况的图表分析
- 通话效果的数据可视化
- 地理分布数据使用Leaflet地图展示

## 核心业务概念

### 随访流程
1. 患者信息录入和管理
2. AI智能电话外呼
3. 通话记录和质量评估
4. 数据分析和报告生成

### 数据模型理解
详细的数据库模式参考 [DATABASE_SCHEMA.md](mdc:DATABASE_SCHEMA.md)：

**Personnel集合字段**：
- 患者基本信息（姓名、年龄、联系方式等）
- 医疗相关信息（病史、诊断等）
- 随访状态和记录

**Call Records集合字段**：
- 通话基本信息（时间、时长、状态等）
- 通话内容和质量评估
- AI训练相关数据

## 用户界面设计原则

### 医疗系统UI/UX要求
- 界面简洁专业，符合医疗行业规范
- 重要操作要有确认机制
- 敏感数据要有适当的权限控制
- 响应式设计适配不同设备

### 数据展示要求
- 患者信息展示要清晰易读
- 统计数据要有直观的图表展示
- 搜索和筛选功能要便捷高效
- 导出功能要支持常见格式

### 错误处理
- 网络错误要有友好提示
- 数据验证错误要明确指出问题
- 系统异常要有降级处理方案

## 开发注意事项



### 性能优化
- 大量患者数据要实现分页加载
- 图表数据要考虑数据量大的情况


### 兼容性
- 支持主流浏览器
- 移动端要有良好的适配




- 考虑网络不稳定情况下的用户体