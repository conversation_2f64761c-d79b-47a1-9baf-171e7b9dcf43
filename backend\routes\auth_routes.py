from flask import Blueprint, request, jsonify
from datetime import datetime
from bson import ObjectId
from database import get_db
from auth.auth import generate_token, verify_password, require_auth

auth_bp = Blueprint('auth', __name__, url_prefix='/api/auth')

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        db = get_db()
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({
                'success': False,
                'error': '用户名和密码不能为空'
            }), 400
        
        # 查找用户
        user = db.sys_info.find_one({
            'doc_type': 'user_account',
            'username': username,
            'is_active': True
        })
        
        if not user:
            return jsonify({
                'success': False,
                'error': '用户名或密码错误'
            }), 401
        
        # 验证密码
        if not verify_password(password, user['password_hash']):
            return jsonify({
                'success': False,
                'error': '用户名或密码错误'
            }), 401
        
        # 生成token
        token = generate_token(user)
        
        # 更新登录信息
        db.sys_info.update_one(
            {'_id': user['_id']},
            {
                '$set': {
                    'last_login': datetime.utcnow()
                },
                '$inc': {
                    'login_count': 1
                }
            }
        )
        
        return jsonify({
            'success': True,
            'data': {
                'token': token,
                'user': {
                    'id': str(user['_id']),
                    'username': user['username'],
                    'role': user['role'],
                    'full_name': user['full_name'],
                    'email': user['email']
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@auth_bp.route('/verify', methods=['GET'])
@require_auth
def verify_token_api():
    """验证token有效性"""
    try:
        db = get_db()
        # 获取完整用户信息
        user = db.sys_info.find_one({
            'doc_type': 'user_account',
            '_id': ObjectId(request.current_user['user_id']),
            'is_active': True
        })
        
        if not user:
            return jsonify({
                'success': False,
                'error': '用户不存在或已被禁用'
            }), 401
        
        return jsonify({
            'success': True,
            'data': {
                'user': {
                    'id': str(user['_id']),
                    'username': user['username'],
                    'role': user['role'],
                    'full_name': user['full_name'],
                    'email': user['email']
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@auth_bp.route('/logout', methods=['POST'])
@require_auth
def logout():
    """用户登出（客户端清除token即可）"""
    return jsonify({
        'success': True,
        'message': '登出成功'
    }) 