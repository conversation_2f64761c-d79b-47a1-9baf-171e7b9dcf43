import os
from typing import Dict, Any
from datetime import timedelta

# 导入端口管理器
try:
    from utils.port_manager import PortManager
except ImportError:
    PortManager = None

class Config:
    """应用配置类"""
    
    # JWT配置
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'your-secret-key-change-in-production')
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    
    # MongoDB配置
    MONGO_URL = os.getenv('MONGO_URL', 'mongodb://localhost:27017')
    DATABASE_NAME = 'med_call_records'
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'DEBUG')
    
    # Flask配置
    DEBUG = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    HOST = os.getenv('FLASK_HOST', '0.0.0.0')
    PORT = int(os.getenv('FLASK_PORT', 5000))
    
    @classmethod
    def get_available_port(cls) -> int:
        """获取可用端口，处理端口冲突"""
        if PortManager:
            try:
                port, message = PortManager.handle_port_conflict(cls.PORT, auto_resolve=True)
                if port != cls.PORT:
                    print(f"⚠️  {message}")
                return port
            except Exception as e:
                print(f"⚠️  端口管理器出错: {e}")
        return cls.PORT
    
    # CORS配置
    CORS_ORIGINS = os.getenv('CORS_ORIGINS', 'http://localhost:5173').split(',')
    
    # API配置
    API_PREFIX = '/api'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 分页配置
    DEFAULT_PAGE_SIZE = 20
    MAX_PAGE_SIZE = 100
    
    @classmethod
    def get_mongo_config(cls) -> Dict[str, Any]:
        """获取MongoDB配置"""
        return {
            'url': cls.MONGO_URL,
            'database': cls.DATABASE_NAME
        }
    
    @classmethod
    def get_flask_config(cls) -> Dict[str, Any]:
        """获取Flask配置"""
        return {
            'SECRET_KEY': cls.JWT_SECRET_KEY,
            'DEBUG': cls.DEBUG,
            'MAX_CONTENT_LENGTH': cls.MAX_CONTENT_LENGTH
        }

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    JWT_SECRET_KEY = os.getenv('SECRET_KEY', os.urandom(32).hex())

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DATABASE_NAME = 'medical_followup_test'

# 配置映射
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config(env: str = None) -> Config:
    """获取配置对象"""
    if env is None:
        env = os.getenv('FLASK_ENV', 'default')
    
    return config_map.get(env, DevelopmentConfig) 