/**
 * AI电话机器人调度器服务
 * 封装所有调度器相关的API调用
 */

export interface SchedulerStatus {
  success: boolean;
  status: 'running' | 'stopped';
  message: string;
  pid?: number;
}

export interface ApiResponse {
  success: boolean;
  message: string;
  pid?: number;
}

export interface HealthResponse {
  success: boolean;
  message: string;
  scheduler_available: boolean;
}

class SchedulerService {
  private readonly baseUrl: string;

  constructor(baseUrl?: string) {
    if (baseUrl) {
      this.baseUrl = baseUrl;
    } else {
      // 使用与api.ts相同的硬编码服务器地址
      this.baseUrl = 'http://114.132.154.140:5000/api/scheduler';
    }
    
    // 调试信息：显示实际使用的baseUrl
    console.log('🔧 SchedulerService 初始化，baseUrl:', this.baseUrl);
  }

  /**
   * 健康检查
   */
  async checkHealth(): Promise<HealthResponse> {
    const response = await fetch(`${this.baseUrl}/health`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  /**
   * 获取调度器状态
   */
  async getStatus(): Promise<SchedulerStatus> {
    const url = `${this.baseUrl}/status`;
    console.log('📡 调度器状态请求URL:', url);
    
    try {
      const response = await fetch(url);
      console.log('📡 响应状态:', response.status);
      console.log('📡 响应头:', response.headers.get('content-type'));
      
      if (!response.ok) {
        // 尝试读取错误响应的内容
        const errorText = await response.text();
        console.error('❌ 错误响应内容:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, content: ${errorText.slice(0, 200)}`);
      }
      
      const result = await response.json();
      console.log('✅ 成功响应:', result);
      return result;
    } catch (error) {
      console.error('❌ 请求失败:', error);
      throw error;
    }
  }

  /**
   * 启动调度器
   */
  async start(): Promise<ApiResponse> {
    const response = await fetch(`${this.baseUrl}/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  /**
   * 停止调度器
   */
  async stop(): Promise<ApiResponse> {
    const response = await fetch(`${this.baseUrl}/stop`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  /**
   * 重启调度器
   */
  async restart(): Promise<ApiResponse> {
    const response = await fetch(`${this.baseUrl}/restart`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  /**
   * 刷新人员名单
   */
  async refreshPersonnel(): Promise<ApiResponse> {
    const response = await fetch(`${this.baseUrl}/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  /**
   * 立即拨号
   */
  async makeImmediateCall(phoneNumber: string): Promise<ApiResponse> {
    const response = await fetch(`${this.baseUrl}/call/immediate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phone: phoneNumber }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  /**
   * 验证电话号码格式
   */
  validatePhoneNumber(phone: string): boolean {
    // 中国手机号码正则验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone.trim());
  }

  /**
   * 格式化状态显示
   */
  formatStatus(status: SchedulerStatus): string {
    return status.status === 'running' ? '运行中' : '已停止';
  }

  /**
   * 获取状态颜色类
   */
  getStatusColorClass(status: SchedulerStatus): string {
    return status.status === 'running' 
      ? 'bg-green-100 text-green-800' 
      : 'bg-red-100 text-red-800';
  }
}

// 创建单例实例，确保使用正确的API地址
export const schedulerService = new SchedulerService();

// 导出类型
export type { SchedulerService };

// 错误处理工具函数
export const handleSchedulerError = (error: unknown): string => {
  if (error instanceof Error) {
    if (error.message.includes('Failed to fetch')) {
      return '网络连接失败，请检查后端服务是否运行';
    }
    if (error.message.includes('HTTP error')) {
      return '服务器响应错误，请稍后重试';
    }
    return error.message;
  }
  return '未知错误，请稍后重试';
};

// 成功消息格式化
export const formatSuccessMessage = (operation: string): string => {
  const messages: Record<string, string> = {
    start: '🚀 调度器启动成功！',
    stop: '⏹️ 调度器已停止！',
    restart: '🔄 调度器重启成功！',
    refresh: '📋 人员名单刷新成功！',
    call: '📞 拨号请求已发送！',
  };
  return messages[operation] || '✅ 操作成功！';
};

// 错误消息格式化
export const formatErrorMessage = (operation: string, error: string): string => {
  const operations: Record<string, string> = {
    start: '启动',
    stop: '停止',
    restart: '重启',
    refresh: '刷新',
    call: '拨号',
  };
  return `❌ ${operations[operation] || '操作'}失败：${error}`;
}; 