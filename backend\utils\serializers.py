from datetime import datetime
from bson import ObjectId
import json

def serialize_doc(doc):
    """将MongoDB文档转换为可序列化的字典"""
    if doc is None:
        return None
        
    if isinstance(doc, dict):
        result = {}
        for key, value in doc.items():
            try:
                if isinstance(value, ObjectId):
                    result[key] = str(value)
                elif isinstance(value, datetime):
                    result[key] = value.isoformat()
                elif isinstance(value, list):
                    result[key] = [serialize_doc(item) for item in value]
                elif isinstance(value, dict):
                    result[key] = serialize_doc(value)
                else:
                    # 测试是否可以JSON序列化
                    json.dumps(value)
                    result[key] = value
            except (TypeError, ValueError) as e:
                print(f"⚠️ 序列化字段 {key} 时出错: {e}, 值类型: {type(value)}, 值: {value}")
                # 如果无法序列化，转换为字符串
                result[key] = str(value)
        return result
    elif isinstance(doc, list):
        return [serialize_doc(item) for item in doc]
    elif isinstance(doc, ObjectId):
        return str(doc)
    elif isinstance(doc, datetime):
        return doc.isoformat()
    else:
        try:
            # 测试是否可以JSON序列化
            json.dumps(doc)
            return doc
        except (TypeError, ValueError):
            return str(doc)

def serialize_user(user_doc):
    """将用户文档转换为前端所需格式，将_id转换为id"""
    if not user_doc:
        return None
    
    result = serialize_doc(user_doc)
    if '_id' in result:
        result['id'] = result.pop('_id')
    return result 