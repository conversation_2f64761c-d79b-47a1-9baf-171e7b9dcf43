{"name": "lianbao_web_data", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit false && vite build", "build:force": "vite build --mode production", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@types/leaflet": "^1.9.14", "@types/leaflet.heat": "^0.2.4", "@types/proj4": "^2.5.5", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "file-saver": "^2.0.5", "leaflet": "^1.9.4", "leaflet.heat": "^0.2.0", "lucide-react": "^0.447.0", "moment": "^2.30.1", "proj4": "^2.15.0", "proj4leaflet": "^1.0.2", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-query": "^3.39.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/file-saver": "^2.0.7", "@types/node": "^22.7.4", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}