# AI电话机器人调度器集成设置说明

## 快速开始

### 1. 确认环境配置

确保以下目录和文件存在：
- AICall工程目录：`D:\57_project_codes\phoneAI\AICall\SuiFangMedCall`
- 调度器管理器：`D:\57_project_codes\phoneAI\AICall\SuiFangMedCall\scheduler_manager.py`

### 2. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 3. 启动Flask应用

```bash
cd backend
python app.py
```

或者使用：

```bash
cd backend
python start.py
```

### 4. 验证API功能

运行测试脚本验证所有功能：

```bash
cd backend
python test_scheduler_api.py
```

## API端点列表

启动成功后，可以通过以下端点管理调度器：

- `GET /api/scheduler/health` - 健康检查
- `GET /api/scheduler/status` - 获取调度器状态
- `POST /api/scheduler/start` - 启动调度器
- `POST /api/scheduler/stop` - 停止调度器
- `POST /api/scheduler/restart` - 重启调度器
- `POST /api/scheduler/refresh` - 刷新人员名单
- `POST /api/scheduler/call/immediate` - 立即拨号

## 前端集成建议

### 1. 创建调度器管理组件

建议在系统设置界面创建一个调度器管理组件，包含以下功能：

```typescript
interface SchedulerStatus {
  success: boolean;
  status: 'running' | 'stopped';
  message: string;
  pid?: number;
}

// 调度器管理服务
class SchedulerService {
  private baseUrl = '/api/scheduler';

  async getStatus(): Promise<SchedulerStatus> {
    const response = await fetch(`${this.baseUrl}/status`);
    return response.json();
  }

  async start(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/start`, { method: 'POST' });
    return response.json();
  }

  async stop(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/stop`, { method: 'POST' });
    return response.json();
  }

  async restart(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/restart`, { method: 'POST' });
    return response.json();
  }
}
```

### 2. 状态监控界面

```jsx
function SchedulerControl() {
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(false);

  // 定期更新状态
  useEffect(() => {
    const interval = setInterval(async () => {
      const status = await schedulerService.getStatus();
      setStatus(status);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="scheduler-control">
      <div className="status-display">
        状态: {status?.status === 'running' ? '运行中' : '已停止'}
        {status?.pid && <span>进程ID: {status.pid}</span>}
      </div>
      
      <div className="control-buttons">
        <button onClick={handleStart} disabled={loading}>启动</button>
        <button onClick={handleStop} disabled={loading}>停止</button>
        <button onClick={handleRestart} disabled={loading}>重启</button>
      </div>
    </div>
  );
}
```

## 故障排除

### 常见问题

1. **调度器模块不可用**
   - 检查AICall工程目录路径是否正确
   - 确认`scheduler_manager.py`文件存在
   - 检查Python路径是否正确添加

2. **API返回503错误**
   - 表示调度器模块导入失败
   - 检查AICall工程目录下的依赖是否完整
   - 查看Flask应用日志获取详细错误信息

3. **调度器启动失败**
   - 检查调度器配置是否正确
   - 确认数据库连接正常
   - 查看调度器相关的配置文件

### 日志查看

Flask应用会记录所有调度器相关的操作日志，可以通过以下方式查看：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 安全考虑

1. **权限控制**：建议只允许管理员访问调度器管理功能
2. **输入验证**：对手机号等输入进行严格验证
3. **操作日志**：记录所有调度器操作的用户和时间
4. **错误处理**：避免在错误信息中暴露敏感信息

## 性能监控

建议添加以下监控指标：

1. 调度器运行状态
2. 进程资源使用情况
3. API响应时间
4. 错误率统计

## 部署注意事项

在生产环境部署时，请注意：

1. 确保AICall工程目录在生产服务器上存在
2. 配置正确的文件路径
3. 设置适当的进程管理策略
4. 配置日志轮转和监控报警 