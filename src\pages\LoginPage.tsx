import React, { useState, useEffect } from 'react';
import { UserCheck, Lock, AlertCircle, Loader2 } from 'lucide-react';
import { TokenManager } from '../services/api';

interface LoginPageProps {
  onLogin: (credentials: { username: string; password: string }) => Promise<void>;
}

const LoginPage: React.FC<LoginPageProps> = ({ onLogin }) => {
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [rememberMe, setRememberMe] = useState(false);

  // 页面加载时检查是否有保存的账号信息
  useEffect(() => {
    const savedCredentials = TokenManager.getSavedCredentials();
    if (savedCredentials) {
      setCredentials(savedCredentials);
      setRememberMe(true);
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      await onLogin(credentials);
      
      // 登录成功后处理记住账号
      if (rememberMe) {
        TokenManager.saveCredentials(credentials.username, credentials.password);
      } else {
        TokenManager.clearSavedCredentials();
      }
      
      // 登录成功后，AuthContext会自动更新用户状态，无需手动处理
    } catch (error: any) {
      setError(error.message || '登录失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCredentials(prev => ({
      ...prev,
      [name]: value
    }));
    
    // 如果用户修改了保存的账号信息，清除错误提示
    if (error) {
      setError('');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        {/* 系统Logo和标题 */}
        <div className="text-center">
          <div className="mx-auto h-20 w-20 bg-blue-600 rounded-full flex items-center justify-center mb-4">
            <UserCheck className="h-10 w-10 text-white" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            医疗随访管理系统
          </h2>
          <p className="text-gray-600 text-sm">
            请使用您的账号登录系统
          </p>
        </div>

        {/* 登录表单 */}
        <div className="bg-white shadow-lg rounded-lg p-8 border border-gray-100">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {/* 错误提示 */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center">
                <AlertCircle className="h-5 w-5 text-red-600 mr-3" />
                <span className="text-red-700 text-sm">{error}</span>
              </div>
            )}

            {/* 用户名输入 */}
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
                用户名
              </label>
              <div className="relative">
                <input
                  id="username"
                  name="username"
                  type="text"
                  required
                  value={credentials.username}
                  onChange={handleInputChange}
                  className="appearance-none relative block w-full px-4 py-3 pl-12 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10"
                  placeholder="请输入用户名"
                  disabled={isLoading}
                />
                <UserCheck className="h-5 w-5 text-gray-400 absolute left-4 top-3.5" />
              </div>
            </div>

            {/* 密码输入 */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                密码
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  value={credentials.password}
                  onChange={handleInputChange}
                  className="appearance-none relative block w-full px-4 py-3 pl-12 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10"
                  placeholder="请输入密码"
                  disabled={isLoading}
                />
                <Lock className="h-5 w-5 text-gray-400 absolute left-4 top-3.5" />
              </div>
            </div>

            {/* 记住账号勾选框 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => {
                    setRememberMe(e.target.checked);
                    // 如果取消勾选，立即清除保存的账号信息
                    if (!e.target.checked) {
                      TokenManager.clearSavedCredentials();
                    }
                  }}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={isLoading}
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                  记住账号
                </label>
              </div>
              
              {/* 清除保存的账号按钮 */}
              {TokenManager.hasSavedCredentials() && (
                <button
                  type="button"
                  onClick={() => {
                    TokenManager.clearSavedCredentials();
                    setCredentials({ username: '', password: '' });
                    setRememberMe(false);
                  }}
                  className="text-xs text-gray-500 hover:text-gray-700 underline"
                  disabled={isLoading}
                >
                  清除保存的账号
                </button>
              )}
            </div>

            {/* 登录按钮 */}
            <div>
              <button
                type="submit"
                disabled={isLoading || !credentials.username || !credentials.password}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                    登录中...
                  </>
                ) : (
                  '登录'
                )}
              </button>
            </div>
          </form>

          {/* 默认账号提示 */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="text-center text-sm text-gray-600">
              <p className="mb-2">两种类型账号:</p>
              <div className="space-y-1 text-xs">
                <p><span className="font-medium">管理员</span> </p>
                <p><span className="font-medium">医生</span> </p>
              </div>
              {rememberMe && (
                <p className="mt-3 text-xs text-blue-600">
                  💡 勾选"记住账号"后，下次访问将自动填充账号密码
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage; 