import React from 'react';

type BadgeVariant = 'active' | 'pending' | 'inactive' | 'completed';

interface StatusBadgeProps {
  status: string;
  variant: BadgeVariant;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, variant }) => {
  const variants: Record<BadgeVariant, string> = {
    active: 'bg-green-100 text-green-600',
    pending: 'bg-yellow-100 text-yellow-600',
    inactive: 'bg-red-100 text-red-600',
    completed: 'bg-blue-100 text-blue-600',
  };

  return (
    <span className={`inline-block px-3 py-1 rounded-full text-xs ${variants[variant]}`}>
      {status}
    </span>
  );
};

export default StatusBadge; 