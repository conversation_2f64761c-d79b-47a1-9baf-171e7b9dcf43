// App.tsx
import React, { useState } from 'react';
import { Patient } from './types';
import { AuthProvider, useAuth } from './contexts/AuthContext';

// 导入页面组件
import DashboardPage from './pages/DashboardPage';
import PatientsPage from './pages/PatientsPage';
import ProgressPage from './pages/ProgressPage';
import RecordingsPage from './pages/RecordingsPage';
import TrainingDataPage from './pages/TrainingDataPage';
import SettingsPage from './pages/SettingsPage';
import LoginPage from './pages/LoginPage';

// 导入通用组件
import Sidebar from './components/Sidebar';
import PatientDetailModal from './components/PatientDetailModal';
import AddPatientModal from './components/AddPatientModal';
import EditPatientModal from './components/EditPatientModal';

// 加载组件
const LoadingScreen: React.FC = () => (
  <div className="min-h-screen bg-gray-50 flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p className="text-gray-600">系统加载中...</p>
    </div>
  </div>
);

// 主应用内容组件
const AppContent: React.FC = () => {
  const { user, isLoading, login } = useAuth();
  const [activeMenu, setActiveMenu] = useState('dashboard');
  const [showPatientModal, setShowPatientModal] = useState(false);
  const [showAddPatientModal, setShowAddPatientModal] = useState(false);
  const [showEditPatientModal, setShowEditPatientModal] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [editingPatient, setEditingPatient] = useState<Patient | null>(null);
  const [viewingPatientHistory, setViewingPatientHistory] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0); // 用于触发数据刷新

  // 如果正在加载，显示加载界面
  if (isLoading) {
    return <LoadingScreen />;
  }

  // 如果未登录，显示登录页面
  if (!user) {
    return <LoginPage onLogin={login} />;
  }

  // 查看患者通话历史
  const handleViewPatientHistory = (patient: Patient) => {
    setSelectedPatient(patient);
    setViewingPatientHistory(true);
  };

  // 返回患者管理页面
  const handleBackToPatients = () => {
    setViewingPatientHistory(false);
    setSelectedPatient(null);
  };

  // 编辑患者信息
  const handleEditPatient = (patient: Patient) => {
    setEditingPatient(patient);
    setShowEditPatientModal(true);
  };

  // 处理菜单切换
  const handleMenuChange = (menuId: string) => {
    // 当切换到其他菜单时，清除患者历史查看状态
    if (viewingPatientHistory && menuId !== 'patients') {
      setViewingPatientHistory(false);
      setSelectedPatient(null);
    }
  };

  // 处理数据刷新
  const handleDataRefresh = () => {
    console.log('🔄 触发数据刷新，当前refreshKey:', refreshKey);
    setRefreshKey(prev => {
      const newKey = prev + 1;
      console.log('✅ 新的refreshKey:', newKey);
      return newKey;
    });
  };

  const renderPage = () => {
    // 如果正在查看患者通话历史，且当前在患者管理页面，渲染患者通话历史页面
    if (viewingPatientHistory && selectedPatient && activeMenu === 'patients') {
      return (
        <PatientsPage 
          setShowAddPatientModal={setShowAddPatientModal}
          setSelectedPatient={setSelectedPatient}
          onViewPatientHistory={handleViewPatientHistory}
          onEditPatient={handleEditPatient}
          viewingPatientHistory={true}
          selectedPatient={selectedPatient}
          onBack={handleBackToPatients}
          refreshKey={refreshKey} // 添加刷新键
        />
      );
    }

    const dashboardPageProps = {
      setShowPatientModal,
      setShowAddPatientModal,
      setSelectedPatient,
      onEditPatient: handleEditPatient,
      refreshKey // 传递刷新键
    };

    const patientsPageProps = {
      setShowAddPatientModal,
      setSelectedPatient,
      onViewPatientHistory: handleViewPatientHistory,
      onEditPatient: handleEditPatient,
      refreshKey // 传递刷新键
    };

    switch (activeMenu) {
      case 'dashboard':
        return <DashboardPage {...dashboardPageProps} />;
      case 'patients':
        return <PatientsPage {...patientsPageProps} />;
      case 'progress':
        return <ProgressPage />;
      case 'recordings':
        return <RecordingsPage />;
      case 'training-data':
        return <TrainingDataPage />;
      case 'settings':
        return <SettingsPage />;
      default:
        return <DashboardPage {...dashboardPageProps} />;
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar 
        activeMenu={activeMenu} 
        setActiveMenu={setActiveMenu}
        onMenuChange={handleMenuChange}
      />
      <div className="flex-1 overflow-y-auto p-5">
        {renderPage()}
      </div>
      <PatientDetailModal 
        isOpen={showPatientModal} 
        onClose={() => {
          setShowPatientModal(false);
          setSelectedPatient(null);
        }}
        patient={selectedPatient}
      />
      <AddPatientModal 
        isOpen={showAddPatientModal} 
        onClose={() => setShowAddPatientModal(false)}
        onSuccess={handleDataRefresh}
      />
      <EditPatientModal 
        isOpen={showEditPatientModal} 
        onClose={() => {
          setShowEditPatientModal(false);
          setEditingPatient(null);
        }}
        patient={editingPatient}
        onSuccess={handleDataRefresh}
      />
    </div>
  );
};

// 主应用组件
const App: React.FC = () => {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
};

export default App;
