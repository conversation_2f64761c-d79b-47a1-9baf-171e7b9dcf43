"""
调度器管理路由
提供AI电话机器人调度器的管理接口
"""

from flask import Blueprint, jsonify, request
import logging
import sys 

AI_CALL_PATH = r'/home/<USER>/workspace/suifangmedcall'
if AI_CALL_PATH not in sys.path:
    sys.path.append(AI_CALL_PATH)

# 导入调度器管理器函数
try:
    from scheduler_manager import (
        start_scheduler,
        stop_scheduler,
        restart_scheduler,
        get_scheduler_status,
        refresh_personnel,
        make_immediate_call
    )
    SCHEDULER_AVAILABLE = True
except ImportError as e:
    logging.error(f"无法导入调度器管理器: {e}")
    SCHEDULER_AVAILABLE = False

scheduler_bp = Blueprint('scheduler', __name__, url_prefix='/api/scheduler')

def _check_scheduler_availability():
    """检查调度器是否可用"""
    if not SCHEDULER_AVAILABLE:
        return {
            "success": False,
            "message": "调度器模块不可用，请检查AICall工程目录是否正确配置"
        }
    return None

@scheduler_bp.route('/start', methods=['POST'])
def api_start_scheduler():
    """启动调度器"""
    try:
        # 检查调度器可用性
        error_response = _check_scheduler_availability()
        if error_response:
            return jsonify(error_response), 503
        
        result = start_scheduler()
        status_code = 200 if result["success"] else 400
        return jsonify(result), status_code
        
    except Exception as e:
        logging.error(f"启动调度器API错误: {e}")
        return jsonify({
            "success": False,
            "message": f"启动调度器时发生错误: {str(e)}"
        }), 500

@scheduler_bp.route('/stop', methods=['POST'])
def api_stop_scheduler():
    """停止调度器"""
    try:
        # 检查调度器可用性
        error_response = _check_scheduler_availability()
        if error_response:
            return jsonify(error_response), 503
        
        result = stop_scheduler()
        status_code = 200 if result["success"] else 400
        return jsonify(result), status_code
        
    except Exception as e:
        logging.error(f"停止调度器API错误: {e}")
        return jsonify({
            "success": False,
            "message": f"停止调度器时发生错误: {str(e)}"
        }), 500

@scheduler_bp.route('/restart', methods=['POST'])
def api_restart_scheduler():
    """重启调度器"""
    try:
        # 检查调度器可用性
        error_response = _check_scheduler_availability()
        if error_response:
            return jsonify(error_response), 503
        
        result = restart_scheduler()
        status_code = 200 if result["success"] else 400
        return jsonify(result), status_code
        
    except Exception as e:
        logging.error(f"重启调度器API错误: {e}")
        return jsonify({
            "success": False,
            "message": f"重启调度器时发生错误: {str(e)}"
        }), 500

@scheduler_bp.route('/status', methods=['GET'])
def api_get_scheduler_status():
    """获取调度器状态"""
    try:
        # 检查调度器可用性
        error_response = _check_scheduler_availability()
        if error_response:
            return jsonify(error_response), 503
        
        result = get_scheduler_status()
        return jsonify(result), 200
        
    except Exception as e:
        logging.error(f"获取调度器状态API错误: {e}")
        return jsonify({
            "success": False,
            "message": f"获取调度器状态时发生错误: {str(e)}"
        }), 500

@scheduler_bp.route('/refresh', methods=['POST'])
def api_refresh_personnel():
    """刷新人员名单"""
    try:
        # 检查调度器可用性
        error_response = _check_scheduler_availability()
        if error_response:
            return jsonify(error_response), 503
        
        result = refresh_personnel()
        return jsonify(result), 200
        
    except Exception as e:
        logging.error(f"刷新人员名单API错误: {e}")
        return jsonify({
            "success": False,
            "message": f"刷新人员名单时发生错误: {str(e)}"
        }), 500

@scheduler_bp.route('/call/immediate', methods=['POST'])
def api_make_immediate_call():
    """立即拨打电话"""
    try:
        # 检查调度器可用性
        error_response = _check_scheduler_availability()
        if error_response:
            return jsonify(error_response), 503
        
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "message": "请求数据格式错误"
            }), 400
        
        phone_number = data.get('phone')
        call_id = data.get('obj_id')  # 可选的call_id参数（前端发送的是obj_id字段名）
        
        if not phone_number:
            return jsonify({
                "success": False,
                "message": "缺少手机号参数"
            }), 400
        
        # 简单的手机号格式验证
        if not isinstance(phone_number, str) or len(phone_number.strip()) == 0:
            return jsonify({
                "success": False,
                "message": "手机号格式不正确"
            }), 400
        
        # 调用make_immediate_call函数，传入call_id参数
        if call_id:
            result = make_immediate_call(phone_number.strip(), obj_id=call_id)
        else:
            result = make_immediate_call(phone_number.strip())
            
        status_code = 200 if result["success"] else 400
        return jsonify(result), status_code
        
    except Exception as e:
        logging.error(f"立即拨号API错误: {e}")
        return jsonify({
            "success": False,
            "message": f"立即拨号时发生错误: {str(e)}"
        }), 500

@scheduler_bp.route('/health', methods=['GET'])
def api_scheduler_health():
    """调度器健康检查"""
    try:
        return jsonify({
            "success": True,
            "message": "调度器API正常",
            "scheduler_available": SCHEDULER_AVAILABLE
        }), 200
        
    except Exception as e:
        logging.error(f"调度器健康检查API错误: {e}")
        return jsonify({
            "success": False,
            "message": f"健康检查失败: {str(e)}"
        }), 500 