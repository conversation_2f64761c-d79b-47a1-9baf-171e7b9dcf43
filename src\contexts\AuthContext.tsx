import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI, TokenManager } from '../services/api';

interface User {
  id: string;
  username: string;
  role: string;
  full_name: string;
  email: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (credentials: { username: string; password: string }) => Promise<void>;
  logout: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 检查现有token
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = TokenManager.getToken();
        if (token) {
          // 验证token有效性
          const response = await authAPI.verify();
          setUser(response.user);
        }
      } catch (error) {
        console.error('Token验证失败:', error);
        TokenManager.removeToken();
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (credentials: { username: string; password: string }) => {
    const response = await authAPI.login(credentials);
    setUser(response.user);
  };

  const logout = async () => {
    await authAPI.logout();
    setUser(null);
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    switch (permission) {
      case 'admin':
        return user.role === '管理员';
      case 'settings':
        return user.role === '管理员'; // 只有管理员能看到系统设置
      default:
        return true; // 其他功能所有登录用户都能访问
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      isLoading,
      login,
      logout,
      hasPermission
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 