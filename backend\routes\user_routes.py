from flask import Blueprint, request, jsonify
from datetime import datetime
from bson import ObjectId
from database import get_db
from auth.auth import require_auth, require_admin, hash_password
from utils.serializers import serialize_user

user_bp = Blueprint('users', __name__, url_prefix='/api/users')

@user_bp.route('', methods=['GET'])
@require_auth
@require_admin
def get_users():
    """获取用户列表（只返回医生账号）"""
    try:
        db = get_db()
        # 只获取医生账号
        users = list(db.sys_info.find({
            'doc_type': 'user_account',
            'role': '医生'
        }))
        
        # 移除密码哈希字段
        for user in users:
            user.pop('password_hash', None)
        
        return jsonify({
            'success': True,
            'data': [serialize_user(user) for user in users]
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@user_bp.route('', methods=['POST'])
@require_auth
@require_admin
def create_user():
    """创建新用户（只能创建医生账号）"""
    try:
        db = get_db()
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['username', 'password', 'full_name', 'email']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'缺少必填字段: {field}'
                }), 400
        
        # 检查用户名是否已存在
        existing_user = db.sys_info.find_one({
            'doc_type': 'user_account',
            'username': data['username']
        })
        
        if existing_user:
            return jsonify({
                'success': False,
                'error': '用户名已存在'
            }), 400
        
        # 强制设置为医生角色
        user_data = {
            'doc_type': 'user_account',
            'username': data['username'],
            'password_hash': hash_password(data['password']),
            'role': '医生',  # 强制设置为医生
            'full_name': data['full_name'],
            'email': data['email'],
            'phone': data.get('phone', ''),
            'is_active': True,
            'login_count': 0,
            'last_login': None,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        
        result = db.sys_info.insert_one(user_data)
        
        # 返回创建的用户信息（不包含密码）
        created_user = db.sys_info.find_one({'_id': result.inserted_id})
        created_user.pop('password_hash', None)
        
        return jsonify({
            'success': True,
            'data': serialize_user(created_user)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@user_bp.route('/<user_id>', methods=['PUT'])
@require_auth
@require_admin
def update_user(user_id):
    """更新用户信息"""
    try:
        db = get_db()
        data = request.get_json()
        
        # 查找用户
        user = db.sys_info.find_one({
            '_id': ObjectId(user_id),
            'doc_type': 'user_account',
            'role': '医生'  # 只能更新医生账号
        })
        
        if not user:
            return jsonify({
                'success': False,
                'error': '用户不存在或无权限修改'
            }), 404
        
        # 准备更新数据
        update_data = {
            'updated_at': datetime.utcnow()
        }
        
        # 可更新的字段
        allowed_fields = ['full_name', 'email', 'phone', 'is_active']
        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field]
        
        # 如果要更新密码
        if 'password' in data and data['password']:
            update_data['password_hash'] = hash_password(data['password'])
        
        # 如果要更新用户名，检查是否重复
        if 'username' in data and data['username'] != user['username']:
            existing = db.sys_info.find_one({
                'doc_type': 'user_account',
                'username': data['username'],
                '_id': {'$ne': ObjectId(user_id)}
            })
            if existing:
                return jsonify({
                    'success': False,
                    'error': '用户名已被使用'
                }), 400
            update_data['username'] = data['username']
        
        # 执行更新
        result = db.sys_info.update_one(
            {'_id': ObjectId(user_id)},
            {'$set': update_data}
        )
        
        if result.modified_count > 0:
            # 返回更新后的用户信息
            updated_user = db.sys_info.find_one({'_id': ObjectId(user_id)})
            updated_user.pop('password_hash', None)
            
            return jsonify({
                'success': True,
                'data': serialize_user(updated_user)
            })
        else:
            return jsonify({
                'success': True,
                'message': '没有数据被修改'
            })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@user_bp.route('/<user_id>', methods=['DELETE'])
@require_auth
@require_admin
def delete_user(user_id):
    """删除用户（软删除，设置为未激活）"""
    try:
        db = get_db()
        # 查找用户
        user = db.sys_info.find_one({
            '_id': ObjectId(user_id),
            'doc_type': 'user_account',
            'role': '医生'  # 只能删除医生账号
        })
        
        if not user:
            return jsonify({
                'success': False,
                'error': '用户不存在或无权限删除'
            }), 404
        
        # 软删除：设置为未激活
        result = db.sys_info.update_one(
            {'_id': ObjectId(user_id)},
            {'$set': {
                'is_active': False,
                'updated_at': datetime.utcnow()
            }}
        )
        
        if result.modified_count > 0:
            return jsonify({
                'success': True,
                'message': '用户已停用'
            })
        else:
            return jsonify({
                'success': False,
                'error': '删除失败'
            }), 500
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500 