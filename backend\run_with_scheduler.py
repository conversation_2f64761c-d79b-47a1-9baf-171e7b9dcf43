#!/usr/bin/env python3
"""
带调度器功能的Flask应用启动脚本
"""

import os
import sys
import logging
from pathlib import Path

def setup_environment():
    """设置环境和路径"""
    # 添加AICall工程目录到Python路径
    ai_call_path = r'D:\57_project_codes\phoneAI\AICall\SuiFangMedCall'
    
    print(f"🔧 配置AICall工程路径: {ai_call_path}")
    
    if not os.path.exists(ai_call_path):
        print(f"⚠️  警告: AICall工程目录不存在: {ai_call_path}")
        print("调度器功能将不可用，但Flask应用仍可正常运行")
    else:
        print(f"✅ AICall工程目录存在")
        
        # 检查关键文件
        scheduler_file = os.path.join(ai_call_path, 'scheduler_manager.py')
        if os.path.exists(scheduler_file):
            print(f"✅ 调度器管理器文件存在")
        else:
            print(f"⚠️  警告: 调度器管理器文件不存在: {scheduler_file}")
    
    # 将路径添加到sys.path（已在app.py中处理，这里只是确认）
    if ai_call_path not in sys.path:
        sys.path.append(ai_call_path)
        print(f"✅ 已添加到Python路径")

def check_dependencies():
    """检查依赖项"""
    print("\n📦 检查依赖项...")
    
    required_packages = [
        'flask', 'flask_cors', 'pymongo', 
        'motor', 'pyjwt', 'bcrypt', 'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖项: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖项已安装")
    return True

def test_scheduler_import():
    """测试调度器模块导入"""
    print("\n🤖 测试调度器模块导入...")
    
    try:
        from scheduler_manager import (
            start_scheduler,
            stop_scheduler,
            restart_scheduler,
            get_scheduler_status,
            refresh_personnel,
            make_immediate_call
        )
        print("✅ 调度器模块导入成功")
        print("✅ 所有调度器API函数可用")
        return True
    except ImportError as e:
        print(f"❌ 调度器模块导入失败: {e}")
        print("调度器功能将不可用，但Flask应用仍可正常运行")
        return False

def start_flask_app():
    """启动Flask应用"""
    print("\n🚀 启动Flask应用...")
    
    try:
        from app import create_app
        from database import init_db
        
        # 创建应用
        app = create_app()
        
        # 初始化数据库
        print("🔗 初始化数据库连接...")
        init_db()
        print("✅ 数据库连接成功")
        
        # 启动应用
        print("🌐 启动Web服务器...")
        print("📍 访问地址: http://localhost:5000")
        print("📋 调度器API: http://localhost:5000/api/scheduler/health")
        print("🛑 按 Ctrl+C 停止服务器")
        
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=True
        )
        
    except Exception as e:
        print(f"❌ 启动Flask应用失败: {e}")
        logging.exception("Flask应用启动异常")
        sys.exit(1)

def main():
    """主函数"""
    print("🏥 医疗随访管理系统 - AI电话机器人调度器")
    print("=" * 60)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 环境设置
    setup_environment()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 测试调度器导入
    scheduler_available = test_scheduler_import()
    
    if scheduler_available:
        print("\n🎉 调度器功能完全可用！")
    else:
        print("\n⚠️  调度器功能不可用，但基础功能正常")
    
    print("\n" + "=" * 60)
    
    # 启动Flask应用
    start_flask_app()

if __name__ == '__main__':
    main() 